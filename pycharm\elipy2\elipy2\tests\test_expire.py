"""
test_expire.py
"""

import json
import os
import pytest
from mock import patch, MagicMock
import elipy2
import elipy2.bilbo
import elipy2.core
import elipy2.filer
from elipy2 import build_metadata, build_metadata_utils
from elipy2.expire import ExpireUtils

from elipy2.exceptions import ELIPYException


@patch("elipy2.telemetry.upload_metrics", MagicMock())
class TestExpire:  # pylint: disable=too-many-public-methods
    @staticmethod
    def list_of_one_build():
        return [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\some\\where",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                    },
                }
            )
        ]

    @pytest.fixture()
    def fixture_builds(self):
        return [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {"created": "1", "type": "code"},
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {"created": "1", "updated": "2", "type": "drone"},
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-28",
                        "deleted": "5",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {"_id": "\\\\filer.test\\builds\\dice\\", "_source": {}}
            ),
            elipy2.bilbo.Build().from_dict({"_id": "\\\\filer.test\\builds\\dice\\"}),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice'\\zero",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\one",
                    "_source": {
                        "created": "1",
                        "updated": "2021-10-26T14:10:45.764266",
                        "in_use_until": "2021-10-26T14:10:45.764266",
                        "type": "code",
                    },
                }
            ),
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\\\filer.test\\builds\\dice\\two",
                    "_source": {
                        "created": "1",
                        "updated": "2021-10-20T14:10:45.764266",
                        "in_use_until": "2021-10-20T14:10:45.764266",
                        "type": "code",
                    },
                }
            ),
        ]

    @pytest.fixture(autouse=True)
    def fixture_bilbo_response(self):
        with patch(
            "elipy2.bilbo._BilboElasticSearch.get_all",
            return_value=json.dumps(
                [
                    {
                        "_id": "\\\\not\\a\\real\\bilbo\\address\\1",
                        "_type": "build",
                        "_source": {
                            "changelist": "1234",
                            "deleted": "2018-03-19T17:44:56.321000",
                            "created": "2018-01-01T00:00:01.000000",
                        },
                    },
                    {
                        "_id": "\\\\not\\a\\real\\bilbo\\address\\2",
                        "_type": "build",
                        "_source": {
                            "changelist": "1234",
                            "created": "2018-01-01T00:00:02.000000",
                        },
                    },
                ]
            ),
        ):
            yield

    @pytest.fixture(autouse=True)
    def fixture_delete_folder_with_robocopy(self):
        with patch(
            "elipy2.core.delete_folder_with_robocopy",
            MagicMock(spec=elipy2.core.delete_folder_with_robocopy),
        ) as mock_delete_folder_with_robocopy:
            yield mock_delete_folder_with_robocopy

    @pytest.fixture(autouse=True)
    def mock_delete_folder(self):
        with patch(
            "elipy2.core.delete_folder",
            MagicMock(spec=elipy2.core.delete_folder),
        ) as mocked_function:
            yield mocked_function

    @pytest.fixture(autouse=True)
    def mock_delete_with_onefs_api(self):
        with patch(
            "elipy2.filer.FilerUtils.delete_with_onefs_api",
            MagicMock(spec=elipy2.filer.FilerUtils.delete_with_onefs_api),
        ) as mocked_function:
            yield mocked_function

    @pytest.fixture(autouse=True)
    def fixture_metadata_manager(self):
        with patch(
            "elipy2.build_metadata_utils.setup_metadata_manager",
            MagicMock(
                spec=build_metadata_utils.setup_metadata_manager,
                return_value=MagicMock(spec=build_metadata.BuildMetadataManager),
            ),
        ) as mock_manager:
            yield mock_manager.return_value

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_dry_run(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=True)
        assert fixture_delete_folder_with_robocopy.call_count == 0
        assert mock_delete_with_onefs_api.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_onefs_api(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=False, use_onefs_api=True)
        assert mock_delete_with_onefs_api.call_count == 8
        assert fixture_delete_folder_with_robocopy.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)
        assert mock_delete_with_onefs_api.call_count == 0
        assert fixture_delete_folder_with_robocopy.call_count == 8

    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_use_onefs_api_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_with_onefs_api,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_with_onefs_api.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False, use_onefs_api=True)
        assert mock_logger.error.call_count == len(fixture_builds) + 1

    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.LOGGER")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_logs_error_and_continues_deletion_on_delete_filer_folder_exception(
        self,
        mock_get_builds_to_expire,
        mock_logger,
        mock_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_folder_with_robocopy.side_effect = ELIPYException("Test exception")
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)
        assert mock_logger.error.call_count == len(fixture_builds) + 1

    @patch("elipy2.expire.LOGGER", MagicMock())
    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_handles_exceptions_from_delete_with_onefs_api(
        self,
        mock_get_builds_to_expire,
        mock_delete_with_onefs_api,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_with_onefs_api.side_effect = [
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            ELIPYException("Test exception"),
        ]
        ExpireUtils().expire(
            "//not/a/build", 0, dry_run=False, use_onefs_api=True
        )  # Shouldn't raise exception
        assert mock_delete_with_onefs_api.call_count == 8

    @patch("elipy2.expire.LOGGER", MagicMock())
    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_handles_exceptions_from_delete_filer_folder(
        self,
        mock_get_builds_to_expire,
        mock_delete_folder_with_robocopy,
        fixture_builds,
    ):
        mock_get_builds_to_expire.return_value = (fixture_builds, [])
        mock_delete_folder_with_robocopy.side_effect = [
            None,
            None,
            None,
            None,
            None,
            None,
            None,
            ELIPYException("Test exception"),
        ]
        ExpireUtils().expire("//not/a/build", 0, dry_run=False)  # Shouldn't raise exception
        assert mock_delete_folder_with_robocopy.call_count == 8

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_run(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        builds = TestExpire.list_of_one_build()
        mock_expire.return_value = (builds, [])

        # Mock get_builds_matching to return the build object so delete_build gets called
        fixture_metadata_manager.get_builds_matching.return_value = builds

        ExpireUtils().expire("//not/a/build", 1, dry_run=False)

        # The method should call delete_build with the build ID, not the build object
        fixture_metadata_manager.delete_build.assert_called_once_with(builds[0].id)
        fixture_delete_folder_with_robocopy.assert_called_once_with(builds[0])

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_run_invalid(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        builds_to_delete = [
            elipy2.bilbo.Build().from_dict(
                {
                    "_id": "\\?some\\where*",
                    "_source": {
                        "created": "1",
                        "updated": "2018-10-29 more stuff to sort",
                    },
                }
            )
        ]
        mock_expire.return_value = (builds_to_delete, [])
        ExpireUtils().expire("\\?some\\where*", 1, dry_run=False)
        assert fixture_delete_folder_with_robocopy.call_count == 0
        assert fixture_metadata_manager.delete_build.call_count == 0

    @patch("elipy2.expire.ExpireUtils.get_builds_to_expire")
    def test_expire_response_dry_run(
        self, mock_expire, fixture_delete_folder_with_robocopy, fixture_metadata_manager
    ):
        mock_expire.return_value = (TestExpire.list_of_one_build(), [])
        ExpireUtils().expire("//not/a/build", 1, dry_run=True)
        assert fixture_metadata_manager.delete_build.call_count == 0
        assert fixture_delete_folder_with_robocopy.call_count == 0

    def test_get_builds_to_expire(
        self,
        fixture_metadata_manager,
        fixture_builds,
    ):
        # Test that builds get properly processed through the new integrated logic
        fixture_metadata_manager.get_build_ids.return_value = ["one", "two", "three"]
        fixture_metadata_manager.get_builds_matching.return_value = fixture_builds[0:1]

        # Mock scanning and querying
        with patch("elipy2.expire.ExpireUtils._scan_disk_builds") as mock_scan, patch(
            "elipy2.expire.ExpireUtils._query_all_bilbo_indices_for_build"
        ) as mock_query:
            mock_scan.return_value = ["one", "two", "three"]
            mock_query.return_value = fixture_builds[0:1]

            result = ExpireUtils().get_builds_to_expire("\\\\not\\a\\real\\bilbo\\address", 5, 1)
            assert result == ([], [])

    def test_safe_execute_decorator_success(self):
        """Test safe_execute decorator with successful execution."""
        from elipy2.expire import safe_execute

        @safe_execute(default_return="default")
        def test_function():
            return "success"

        result = test_function()
        assert result == "success"

    def test_safe_execute_decorator_exception(self):
        """Test safe_execute decorator with exception."""
        from elipy2.expire import safe_execute

        @safe_execute(default_return="default")
        def test_function():
            raise ValueError("Test error")

        with patch("elipy2.expire.LOGGER") as mock_logger:
            result = test_function()
            assert result == "default"
            mock_logger.error.assert_called_once()

    def test_safe_execute_decorator_no_logging(self):
        """Test safe_execute decorator with logging disabled."""
        from elipy2.expire import safe_execute

        @safe_execute(default_return="default", log_errors=False)
        def test_function():
            raise ValueError("Test error")

        with patch("elipy2.expire.LOGGER") as mock_logger:
            result = test_function()
            assert result == "default"
            mock_logger.error.assert_not_called()

    def test_is_cl_directory_valid_cl_numbers(self):
        """Test _is_cl_directory with valid CL numbers."""
        expire_utils = ExpireUtils()
        assert expire_utils._is_cl_directory("12345678") is True
        assert expire_utils._is_cl_directory("1234567") is True
        assert expire_utils._is_cl_directory("123456789") is True
        assert expire_utils._is_cl_directory("123456") is False
        assert expire_utils._is_cl_directory("1234567890") is False

    def test_is_cl_directory_cl_underscore_patterns(self):
        """Test _is_cl_directory with CL_CL patterns."""
        expire_utils = ExpireUtils()
        assert expire_utils._is_cl_directory("12345678_87654321") is True
        assert expire_utils._is_cl_directory("1234567_7654321") is True
        assert expire_utils._is_cl_directory("123456789_987654321") is True
        assert expire_utils._is_cl_directory("123456_654321") is False
        assert expire_utils._is_cl_directory("12345678_") is False

    def test_is_cl_directory_invalid_entries(self):
        """Test _is_cl_directory with invalid entries."""
        expire_utils = ExpireUtils()
        assert expire_utils._is_cl_directory("abc") is False
        assert expire_utils._is_cl_directory("abc123") is False
        assert expire_utils._is_cl_directory("123abc") is False
        assert expire_utils._is_cl_directory("") is False
        assert expire_utils._is_cl_directory("12345678abc") is False

    def test_sort_builds_by_cl_valid_paths(self):
        """Test _sort_builds_by_cl with valid build paths."""
        expire_utils = ExpireUtils()
        builds = [
            "\\\\server\\share\\builds\\test\\87654321",
            "\\\\server\\share\\builds\\test\\12345678",
            "\\\\server\\share\\builds\\test\\56789012",
        ]
        sorted_builds = expire_utils._sort_builds_by_cl(builds)
        # Should be sorted by CL number from oldest to latest (smallest to largest)
        expected = [
            "\\\\server\\share\\builds\\test\\12345678",
            "\\\\server\\share\\builds\\test\\56789012",
            "\\\\server\\share\\builds\\test\\87654321",
        ]
        assert sorted_builds == expected

    def test_sort_builds_by_cl_mixed_paths(self):
        """Test _sort_builds_by_cl with mixed path types."""
        expire_utils = ExpireUtils()
        builds = [
            "\\\\server\\share\\builds\\test\\nonumber",
            "\\\\server\\share\\builds\\test\\12345678",
            "\\\\server\\share\\builds\\test\\version\\1.2.3",
        ]
        sorted_builds = expire_utils._sort_builds_by_cl(builds)
        # Paths without CL numbers get 0 as key, so they come first
        # Then paths with CL numbers are sorted by CL value
        # version\1.2.3 has "1" as the last numeric part, so it comes before 12345678
        assert sorted_builds[0] == "\\\\server\\share\\builds\\test\\nonumber"
        assert sorted_builds[1] == "\\\\server\\share\\builds\\test\\version\\1.2.3"
        assert sorted_builds[2] == "\\\\server\\share\\builds\\test\\12345678"

    def test_get_preservation_counters_default(self):
        """Test _get_preservation_counters with default settings."""
        expire_utils = ExpireUtils()

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = []

            counters = expire_utils._get_preservation_counters()

            expected = {
                "promoted": 0,
                "drone": 0,
                "release_candidate": 0,
            }
            assert counters == expected

    def test_get_preservation_counters_with_settings(self):
        """Test _get_preservation_counters with retention settings."""
        expire_utils = ExpireUtils()

        with patch("elipy2.expire.SETTINGS") as mock_settings:

            def mock_get(key, location=None):
                if key == "spin_retention":
                    return [{"main": 3}, {"develop": 2}]
                elif key == "smoke_retention":
                    return [{"release": 5}]
                return []

            mock_settings.get.side_effect = mock_get

            counters = expire_utils._get_preservation_counters()

            expected = {
                "promoted": 0,
                "drone": 0,
                "release_candidate": 0,
                "spin_main": 0,
                "spin_develop": 0,
                "smoke_release": 0,
            }
            assert counters == expected

    def test_get_preservation_counters_exception_handling(self):
        """Test _get_preservation_counters with exception handling."""
        expire_utils = ExpireUtils()

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.side_effect = Exception("Test exception")

            counters = expire_utils._get_preservation_counters()

            expected = {
                "promoted": 0,
                "drone": 0,
                "release_candidate": 0,
            }
            assert counters == expected

    def test_should_preserve_build_with_retention_no_builds(self):
        """Test _should_preserve_build_with_retention with no builds."""
        expire_utils = ExpireUtils()
        counters = {"promoted": 0, "drone": 0, "release_candidate": 0}
        should_preserve, reason = expire_utils._should_preserve_build_with_retention([], counters)
        assert should_preserve is False
        assert reason == ""

    def test_should_preserve_build_with_retention_spin_build(self):
        """Test _should_preserve_build_with_retention with spin build."""
        expire_utils = ExpireUtils()
        counters = {"spin_main": 0}

        mock_bilbo_build = MagicMock()
        mock_bilbo_build.source = {"type": "spin", "branch": "main"}

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = [{"main": 2}]

            should_preserve, reason = expire_utils._should_preserve_build_with_retention(
                [mock_bilbo_build], counters
            )

            assert should_preserve is True
            assert reason == "spin main build"
            assert counters["spin_main"] == 1

    def test_check_build_preservation_no_source(self):
        """Test _check_build_preservation with build without source."""
        expire_utils = ExpireUtils()
        build = MagicMock()
        build.source = None
        counters = {}

        should_preserve, reason = expire_utils._check_build_preservation(build, counters)
        assert should_preserve is False
        assert reason == ""

    def test_check_spin_preservation_valid(self):
        """Test _check_spin_preservation with valid spin build."""
        expire_utils = ExpireUtils()
        source = {"type": "spin", "branch": "main"}
        counters = {"spin_main": 0}

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = [{"main": 2}]

            should_preserve, reason = expire_utils._check_spin_preservation(
                source, "spin", "main", counters
            )

            assert should_preserve is True
            assert reason == "spin main build"

    def test_check_spin_preservation_wrong_type(self):
        """Test _check_spin_preservation with wrong build type."""
        expire_utils = ExpireUtils()
        source = {"type": "smoke", "branch": "main"}
        counters = {"spin_main": 0}

        should_preserve, reason = expire_utils._check_spin_preservation(
            source, "smoke", "main", counters
        )

        assert should_preserve is False
        assert reason == ""

    def test_check_smoke_preservation_valid(self):
        """Test _check_smoke_preservation with valid smoke build."""
        expire_utils = ExpireUtils()
        source = {"type": "smoke", "branch": "release"}
        counters = {"smoke_release": 0}

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = [{"release": 3}]

            should_preserve, reason = expire_utils._check_smoke_preservation(
                source, "smoke", "release", counters
            )

            assert should_preserve is True
            assert reason == "smoke release build"

    def test_check_branch_retention_valid(self):
        """Test _check_branch_retention with valid branch retention."""
        expire_utils = ExpireUtils()
        counters = {"spin_main": 0}

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = [{"main": 2}]

            should_preserve, reason = expire_utils._check_branch_retention("spin", "main", counters)

            assert should_preserve is True
            assert reason == "spin main build"
            assert counters["spin_main"] == 1

    def test_check_branch_retention_exceeded(self):
        """Test _check_branch_retention when retention limit is exceeded."""
        expire_utils = ExpireUtils()
        counters = {"spin_main": 2}

        with patch("elipy2.expire.SETTINGS") as mock_settings:
            mock_settings.get.return_value = [{"main": 2}]

            should_preserve, reason = expire_utils._check_branch_retention("spin", "main", counters)

            assert should_preserve is False
            assert reason == ""

    def test_check_release_candidate_preservation_valid(self):
        """Test _check_release_candidate_preservation with valid RC build."""
        expire_utils = ExpireUtils()
        source = {"release_candidate": True}
        counters = {"release_candidate": 0}

        should_preserve, reason = expire_utils._check_release_candidate_preservation(
            source, "code", "main", counters
        )

        assert should_preserve is True
        assert reason == "release candidate build"
        assert counters["release_candidate"] == 1

    def test_check_release_candidate_preservation_exceeded(self):
        """Test _check_release_candidate_preservation when limit exceeded."""
        expire_utils = ExpireUtils()
        source = {"release_candidate": True}
        counters = {"release_candidate": 1}

        should_preserve, reason = expire_utils._check_release_candidate_preservation(
            source, "code", "main", counters
        )

        assert should_preserve is False
        assert reason == ""

    def test_check_promoted_preservation_valid(self):
        """Test _check_promoted_preservation with promoted build."""
        expire_utils = ExpireUtils()
        source = {"build_promotion_level": "promoted"}
        counters = {"promoted": 0}

        should_preserve, reason = expire_utils._check_promoted_preservation(
            source, "code", "main", counters
        )

        assert should_preserve is True
        assert reason == "promoted build"
        assert counters["promoted"] == 1

    def test_check_promoted_preservation_exceeded(self):
        """Test _check_promoted_preservation when limit exceeded."""
        expire_utils = ExpireUtils()
        source = {"build_promotion_level": "promoted"}
        counters = {"promoted": 1}

        should_preserve, reason = expire_utils._check_promoted_preservation(
            source, "code", "main", counters
        )

        assert should_preserve is False
        assert reason == ""

    def test_check_drone_preservation_valid(self):
        """Test _check_drone_preservation with drone build."""
        expire_utils = ExpireUtils()
        source = {"type": "drone"}
        counters = {"drone": 0}

        should_preserve, reason = expire_utils._check_drone_preservation(
            source, "drone", "main", counters
        )

        assert should_preserve is True
        assert reason == "drone build"
        assert counters["drone"] == 1

    def test_check_drone_preservation_exceeded(self):
        """Test _check_drone_preservation when limit exceeded."""
        expire_utils = ExpireUtils()
        source = {"type": "drone"}
        counters = {"drone": 2}

        should_preserve, reason = expire_utils._check_drone_preservation(
            source, "drone", "main", counters
        )

        assert should_preserve is False
        assert reason == ""

    def test_has_invalid_filename_valid(self):
        """Test _has_invalid_filename with valid filename."""
        expire_utils = ExpireUtils()
        build = "\\\\server\\share\\builds\\test\\12345678"
        assert expire_utils._has_invalid_filename(build) is False

    def test_has_invalid_filename_invalid(self):
        """Test _has_invalid_filename with invalid filename."""
        expire_utils = ExpireUtils()
        build1 = "\\\\server\\share\\builds\\test\\12345?678"
        build2 = "\\\\server\\share\\builds\\test\\12345*678"
        assert expire_utils._has_invalid_filename(build1) is True
        assert expire_utils._has_invalid_filename(build2) is True

    def test_is_safe_to_cleanup_safe_paths(self):
        """Test _is_safe_to_cleanup with safe paths."""
        expire_utils = ExpireUtils()
        safe_paths = [
            "\\\\server\\share\\autobuilds\\frosty\\game\\branch",
            "\\\\server\\share\\builds\\webexport\\version",
            "\\\\server\\share\\glacier\\builds\\test",
        ]

        for path in safe_paths:
            assert expire_utils._is_safe_to_cleanup(path) is True

    def test_is_safe_to_cleanup_unsafe_paths(self):
        """Test _is_safe_to_cleanup with unsafe paths."""
        expire_utils = ExpireUtils()
        unsafe_paths = [
            "\\\\server",
            "\\\\server\\share",
            "\\\\server\\share\\toplevel",
            "\\\\server\\share\\random\\path",
        ]

        for path in unsafe_paths:
            assert expire_utils._is_safe_to_cleanup(path) is False

    def test_is_directory_empty_empty(self):
        """Test _is_directory_empty with empty directory."""
        expire_utils = ExpireUtils()

        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = []

            assert expire_utils._is_directory_empty("test_path") is True

    def test_is_directory_empty_not_empty(self):
        """Test _is_directory_empty with non-empty directory."""
        expire_utils = ExpireUtils()

        with patch("os.listdir") as mock_listdir:
            mock_listdir.return_value = ["file1.txt", "subdir"]

            assert expire_utils._is_directory_empty("test_path") is False

    def test_is_directory_empty_exception(self):
        """Test _is_directory_empty with exception."""
        expire_utils = ExpireUtils()

        with patch("os.listdir") as mock_listdir:
            mock_listdir.side_effect = OSError("Permission denied")

            assert expire_utils._is_directory_empty("test_path") is False

    @patch("os.path.exists")
    @patch("os.listdir")
    def test_scan_frosty_builds_fixed_depth_no_path(self, mock_listdir, mock_exists):
        """Test _scan_frosty_builds_fixed_depth with non-existent path."""
        expire_utils = ExpireUtils()
        mock_exists.return_value = False

        builds = expire_utils._scan_frosty_builds_fixed_depth("nonexistent_path")
        assert builds == []

    @patch("os.path.exists")
    @patch("os.listdir")
    @patch("os.path.isdir")
    def test_scan_frosty_builds_fixed_depth_branch_path(
        self, mock_isdir, mock_listdir, mock_exists
    ):
        """Test _scan_frosty_builds_fixed_depth with branch path."""
        expire_utils = ExpireUtils()
        mock_exists.return_value = True
        mock_isdir.return_value = True

        # Mock directory structure for branch path
        # Need to provide enough return values for all nested listdir calls
        mock_listdir.side_effect = [
            ["12345678", "87654321"],
            ["12345678", "87654321"],
            ["branch1", "branch2"],
            ["98765432", "54321098"],
            ["11111111", "22222222"],
            ["branch1", "branch2"],
            ["98765432", "54321098"],
            ["11111111", "22222222"],
        ]

        builds = expire_utils._scan_frosty_builds_fixed_depth("frosty/BattlefieldGame/kin-stage")

        assert len(builds) == 8

    @patch("os.path.exists")
    @patch("os.listdir")
    @patch("os.path.isdir")
    def test_scan_webexport_builds_fixed_depth_normal(self, mock_isdir, mock_listdir, mock_exists):
        """Test _scan_webexport_builds_fixed_depth with normal structure."""
        expire_utils = ExpireUtils()
        mock_exists.return_value = True
        mock_isdir.return_value = True

        # Mock directory structure: webexport/branch/version/CL
        mock_listdir.side_effect = [
            ["1.2.3", "2.3.4"],  # Version directories
            ["12345678_87654321"],  # CL directories under first version
            ["98765432_54321098"],  # CL directories under second version
        ]

        builds = expire_utils._scan_webexport_builds_fixed_depth("webexport/kin-live")

        # Should find 2 builds
        assert len(builds) == 2

    @patch("os.path.exists")
    @patch("os.listdir")
    def test_scan_webexport_builds_fixed_depth_no_path(self, mock_listdir, mock_exists):
        """Test _scan_webexport_builds_fixed_depth with non-existent path."""
        expire_utils = ExpireUtils()
        mock_exists.return_value = False

        builds = expire_utils._scan_webexport_builds_fixed_depth("nonexistent_path")
        assert builds == []

    @patch("os.path.exists")
    @patch("os.listdir")
    @patch("os.path.isdir")
    def test_scan_frosty_branch_builds_normal(self, mock_isdir, mock_listdir, mock_exists):
        """Test _scan_frosty_branch_builds with normal structure."""
        expire_utils = ExpireUtils()
        mock_isdir.return_value = True

        # Mock directory structure: branch/CL1/branch2/CL2
        # Need to provide enough return values for all nested listdir calls
        mock_listdir.side_effect = [
            ["12345678", "87654321"],
            ["branch1", "branch2"],
            ["98765432", "54321098"],
            ["11111111", "22222222"],
            ["branch1", "branch2"],
            ["98765432", "54321098"],
            ["11111111", "22222222"],
        ]

        builds = expire_utils._scan_frosty_branch_builds("frosty/BattlefieldGame/branch")

        assert len(builds) == 8

    @patch("os.path.exists")
    @patch("os.listdir")
    def test_scan_frosty_branch_builds_exception(self, mock_listdir, mock_exists):
        """Test _scan_frosty_branch_builds with exception."""
        expire_utils = ExpireUtils()
        mock_listdir.side_effect = OSError("Permission denied")

        builds = expire_utils._scan_frosty_branch_builds("frosty/BattlefieldGame/branch")
        assert builds == []

    @patch("os.path.getmtime")
    @patch("elipy2.expire.LOGGER")
    def test_process_orphaned_builds_recent_orphan(self, mock_logger, mock_getmtime):
        """Test _process_orphaned_builds with recent orphan build."""
        expire_utils = ExpireUtils()
        # Mock recent build (1 day old)
        from datetime import datetime, timedelta

        recent_time = datetime.now() - timedelta(days=1)
        mock_getmtime.return_value = recent_time.timestamp()

        disk_builds = ["\\\\server\\share\\builds\\test\\12345678"]

        with patch.object(expire_utils, "_query_all_bilbo_indices_for_build", return_value=[]):
            filtered_builds, orphaned_builds = expire_utils._process_orphaned_builds(disk_builds)

            # Recent orphan should be kept for now
            assert len(filtered_builds) == 1
            assert len(orphaned_builds) == 0

    @patch("os.path.getmtime")
    @patch("elipy2.expire.LOGGER")
    def test_process_orphaned_builds_old_orphan(self, mock_logger, mock_getmtime):
        """Test _process_orphaned_builds with old orphan build."""
        expire_utils = ExpireUtils()
        # Mock old build (3 days old)
        from datetime import datetime, timedelta

        old_time = datetime.now() - timedelta(days=3)
        mock_getmtime.return_value = old_time.timestamp()

        disk_builds = ["\\\\server\\share\\builds\\test\\12345678"]

        with patch.object(expire_utils, "_query_all_bilbo_indices_for_build", return_value=[]):
            filtered_builds, orphaned_builds = expire_utils._process_orphaned_builds(disk_builds)

            # Old orphan should be marked for deletion
            assert len(filtered_builds) == 0
            assert len(orphaned_builds) == 1

    @patch("os.path.getmtime")
    @patch("elipy2.expire.LOGGER")
    def test_process_orphaned_builds_deleted_in_bilbo(self, mock_logger, mock_getmtime):
        """Test _process_orphaned_builds with build deleted in Bilbo."""
        expire_utils = ExpireUtils()
        disk_builds = ["\\\\server\\share\\builds\\test\\12345678"]

        # Mock build found in Bilbo but marked as deleted
        mock_build = MagicMock()
        mock_build.deleted = True

        with patch.object(
            expire_utils, "_query_all_bilbo_indices_for_build", return_value=[mock_build]
        ):
            filtered_builds, orphaned_builds = expire_utils._process_orphaned_builds(disk_builds)

            # Build deleted in Bilbo should be marked for deletion
            assert len(filtered_builds) == 0
            assert len(orphaned_builds) == 1

    @patch("os.path.getmtime")
    @patch("elipy2.expire.LOGGER")
    def test_process_orphaned_builds_valid_in_bilbo(self, mock_logger, mock_getmtime):
        """Test _process_orphaned_builds with valid build in Bilbo."""
        expire_utils = ExpireUtils()
        disk_builds = ["\\\\server\\share\\builds\\test\\12345678"]

        # Mock build found in Bilbo and not deleted
        mock_build = MagicMock()
        mock_build.deleted = False

        with patch.object(
            expire_utils, "_query_all_bilbo_indices_for_build", return_value=[mock_build]
        ):
            filtered_builds, orphaned_builds = expire_utils._process_orphaned_builds(disk_builds)

            # Valid build should be kept
            assert len(filtered_builds) == 1
            assert len(orphaned_builds) == 0

    def test_orphan_deletion_sufficient_true(self):
        """Test _orphan_deletion_sufficient returns True when sufficient."""
        expire_utils = ExpireUtils()
        orphaned_builds = ["build1", "build2"]
        disk_builds_sorted = ["build3", "build4"]
        maxamounti = 5

        result = expire_utils._orphan_deletion_sufficient(
            orphaned_builds, disk_builds_sorted, maxamounti
        )
        assert result is True

    def test_orphan_deletion_sufficient_false(self):
        """Test _orphan_deletion_sufficient returns False when not sufficient."""
        expire_utils = ExpireUtils()
        orphaned_builds = ["build1", "build2"]
        disk_builds_sorted = ["build3", "build4", "build5", "build6", "build7"]
        maxamounti = 3

        result = expire_utils._orphan_deletion_sufficient(
            orphaned_builds, disk_builds_sorted, maxamounti
        )
        assert result is False

    def test_no_deletion_needed_true(self):
        """Test _no_deletion_needed returns True when no deletion needed."""
        expire_utils = ExpireUtils()
        orphaned_builds = []
        disk_builds_sorted = ["build1", "build2"]
        maxamounti = 5

        result = expire_utils._no_deletion_needed(orphaned_builds, disk_builds_sorted, maxamounti)
        assert result is True

    def test_no_deletion_needed_false(self):
        """Test _no_deletion_needed returns False when deletion needed."""
        expire_utils = ExpireUtils()
        orphaned_builds = ["build1"]
        disk_builds_sorted = ["build2", "build3"]
        maxamounti = 5

        result = expire_utils._no_deletion_needed(orphaned_builds, disk_builds_sorted, maxamounti)
        assert result is False

    def test_apply_retention_policies_no_rc_retention(self):
        """Test _apply_retention_policies with no RC retention."""
        expire_utils = ExpireUtils()
        disk_builds_sorted = ["build1", "build2", "build3", "build4", "build5"]
        maxamounti = 3
        release_candidates_to_keep_count = 0

        result = expire_utils._apply_retention_policies(
            disk_builds_sorted, maxamounti, release_candidates_to_keep_count
        )

        # Should expire first 2 builds (5 - 3 = 2)
        assert result == ["build1", "build2"]

    def test_apply_retention_policies_with_rc_retention(self):
        """Test _apply_retention_policies with RC retention."""
        expire_utils = ExpireUtils()
        disk_builds_sorted = ["build1", "build2", "build3", "build4", "build5"]
        maxamounti = 3
        release_candidates_to_keep_count = 1

        with patch.object(expire_utils, "_get_preservation_counters", return_value={}):
            with patch.object(expire_utils, "_filter_builds_by_retention", return_value=["build1"]):
                result = expire_utils._apply_retention_policies(
                    disk_builds_sorted, maxamounti, release_candidates_to_keep_count
                )

                # Should return filtered result
                assert result == ["build1"]

    def test_filter_builds_by_retention_preserve_build(self):
        """Test _filter_builds_by_retention with build preservation."""
        expire_utils = ExpireUtils()
        expire_list = ["build1", "build2"]
        preservation_counters = {}
        reserve_list = []

        with patch.object(expire_utils, "_query_bilbo_for_build", return_value=["mock_build"]):
            with patch.object(
                expire_utils,
                "_should_preserve_build_with_retention",
                return_value=(True, "test reason"),
            ):
                result = expire_utils._filter_builds_by_retention(
                    expire_list, preservation_counters, reserve_list
                )

                # Both builds should be preserved (moved to reserve_list)
                assert result == []
                assert len(reserve_list) == 2

    def test_filter_builds_by_retention_no_preserve(self):
        """Test _filter_builds_by_retention with no preservation."""
        expire_utils = ExpireUtils()
        expire_list = ["build1", "build2"]
        preservation_counters = {}
        reserve_list = []

        with patch.object(expire_utils, "_query_bilbo_for_build", return_value=["mock_build"]):
            with patch.object(
                expire_utils, "_should_preserve_build_with_retention", return_value=(False, "")
            ):
                result = expire_utils._filter_builds_by_retention(
                    expire_list, preservation_counters, reserve_list
                )

                # No builds should be preserved
                assert result == ["build1", "build2"]
                assert len(reserve_list) == 0

    def test_filter_builds_by_retention_exception(self):
        """Test _filter_builds_by_retention with exception."""
        expire_utils = ExpireUtils()
        expire_list = ["build1", "build2"]
        preservation_counters = {}
        reserve_list = []

        with patch.object(
            expire_utils, "_query_bilbo_for_build", side_effect=Exception("Test error")
        ):
            result = expire_utils._filter_builds_by_retention(
                expire_list, preservation_counters, reserve_list
            )

            # Exception should be caught, builds should remain in expire list
            assert result == ["build1", "build2"]
            assert len(reserve_list) == 0

    def test_query_bilbo_for_build_success(self, fixture_metadata_manager):
        """Test _query_bilbo_for_build with successful query."""
        expire_utils = ExpireUtils()
        mock_build = MagicMock()
        mock_build.id = "\\\\server\\share\\builds\\test\\12345678"
        fixture_metadata_manager.get_builds_matching.return_value = [mock_build]

        result = expire_utils._query_bilbo_for_build("\\\\server\\share\\builds\\test\\12345678")

        assert result == [mock_build]
        fixture_metadata_manager.get_builds_matching.assert_called_once_with(
            "\\\\server\\share\\builds\\test\\12345678"
        )

    def test_query_bilbo_for_build_no_results(self, fixture_metadata_manager):
        """Test _query_bilbo_for_build with no results."""
        expire_utils = ExpireUtils()
        fixture_metadata_manager.get_builds_matching.return_value = []

        result = expire_utils._query_bilbo_for_build("\\\\server\\share\\builds\\test\\12345678")

        assert result == []

    def test_query_bilbo_for_build_exception(self, fixture_metadata_manager):
        """Test _query_bilbo_for_build with exception."""
        expire_utils = ExpireUtils()
        fixture_metadata_manager.get_builds_matching.side_effect = Exception("Connection error")

        result = expire_utils._query_bilbo_for_build("\\\\server\\share\\builds\\test\\12345678")

        # Should return empty list on exception due to safe_execute decorator
        assert result == []

    @patch("elipy2.expire.SETTINGS")
    @patch("elipy2.expire.BilboUtilsV2")
    def test_query_all_bilbo_indices_for_build_primary_only(
        self, mock_bilbo_v2_class, mock_settings, fixture_metadata_manager
    ):
        """Test _query_all_bilbo_indices_for_build with primary provider only."""
        expire_utils = ExpireUtils()
        mock_build = MagicMock()
        mock_build.id = "\\\\server\\share\\builds\\test\\12345678"

        # Mock primary provider returns builds
        fixture_metadata_manager.get_builds_matching.return_value = [mock_build]

        # Mock no bilbo_url setting
        mock_settings.get.return_value = None

        result = expire_utils._query_all_bilbo_indices_for_build(
            "\\\\server\\share\\builds\\test\\12345678"
        )

        assert result == [mock_build]
        fixture_metadata_manager.get_builds_matching.assert_called_once()

    @patch("elipy2.expire.SETTINGS")
    @patch("elipy2.expire.BilboUtilsV2")
    def test_query_all_bilbo_indices_for_build_with_bilbo_v2(
        self, mock_bilbo_v2_class, mock_settings, fixture_metadata_manager
    ):
        """Test _query_all_bilbo_indices_for_build with bilbo_v2 provider."""
        expire_utils = ExpireUtils()
        mock_build = MagicMock()
        mock_build.id = "\\\\server\\share\\builds\\test\\12345678"

        # Mock primary provider returns builds
        fixture_metadata_manager.get_builds_matching.return_value = [mock_build]

        # Mock bilbo_url setting
        mock_settings.get.return_value = "http://bilbo.example.com"

        # Mock bilbo_v2 provider
        mock_bilbo_v2_instance = MagicMock()
        mock_bilbo_v2_class.return_value = mock_bilbo_v2_instance

        # Mock bilbo_v2 returns additional builds
        mock_v2_build = MagicMock()
        mock_v2_build.id = "\\\\server\\share\\builds\\test\\98765432"
        mock_bilbo_v2_instance.get_builds_matching.return_value = [mock_v2_build]

        result = expire_utils._query_all_bilbo_indices_for_build(
            "\\\\server\\share\\builds\\test\\12345678"
        )

        # Should return builds from both providers
        assert len(result) == 2
        assert mock_build in result
        assert mock_v2_build in result

    @patch("elipy2.expire.LOGGER")
    def test_delete_all_matching_builds_from_bilbo_no_builds(
        self, mock_logger, fixture_metadata_manager
    ):
        """Test _delete_all_matching_builds_from_bilbo with no matching builds."""
        expire_utils = ExpireUtils()
        fixture_metadata_manager.get_builds_matching.return_value = []

        expire_utils._delete_all_matching_builds_from_bilbo(
            "\\\\server\\share\\builds\\test\\12345678"
        )

        # Should not call delete_build
        fixture_metadata_manager.delete_build.assert_not_called()
        mock_logger.info.assert_called_with(
            "No builds found in Bilbo for path: %s, skipping Bilbo deletion",
            "\\\\server\\share\\builds\\test\\12345678",
        )

    @patch("elipy2.expire.LOGGER")
    def test_delete_all_matching_builds_from_bilbo_success(
        self, mock_logger, fixture_metadata_manager
    ):
        """Test _delete_all_matching_builds_from_bilbo with successful deletion."""
        expire_utils = ExpireUtils()
        mock_build = MagicMock()
        mock_build.id = "\\\\server\\share\\builds\\test\\12345678"

        fixture_metadata_manager.get_builds_matching.return_value = [mock_build]

        # Mock verification that build exists
        fixture_metadata_manager.get_builds_matching.side_effect = [
            [mock_build],  # First call returns the build
            [mock_build],  # Second call (verification) returns the build
        ]

        expire_utils._delete_all_matching_builds_from_bilbo(mock_build)

        # Should call delete_build
        fixture_metadata_manager.delete_build.assert_called_once_with(mock_build.id)
        mock_logger.info.assert_called()

    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.LOGGER")
    def test_delete_build_from_disk_robocopy_with_object(self, mock_logger, mock_delete_robocopy):
        """Test _delete_build_from_disk with robocopy and build object."""
        expire_utils = ExpireUtils()
        mock_build = MagicMock()
        mock_build.id = "\\\\server\\share\\builds\\test\\12345678"

        expire_utils._delete_build_from_disk(
            "\\\\server\\share\\builds\\test\\12345678", False, mock_build
        )

        mock_delete_robocopy.assert_called_once_with(mock_build)
        mock_logger.info.assert_called_with(
            "deleting from disk: file:%s", "\\\\server\\share\\builds\\test\\12345678"
        )

    @patch("elipy2.core.delete_folder_with_robocopy")
    @patch("elipy2.expire.LOGGER")
    def test_delete_build_from_disk_robocopy_without_object(
        self, mock_logger, mock_delete_robocopy
    ):
        """Test _delete_build_from_disk with robocopy without build object."""
        expire_utils = ExpireUtils()

        expire_utils._delete_build_from_disk("\\\\server\\share\\builds\\test\\12345678", False)

        mock_delete_robocopy.assert_called_once_with("\\\\server\\share\\builds\\test\\12345678")
        mock_logger.info.assert_called_with(
            "deleting from disk: file:%s", "\\\\server\\share\\builds\\test\\12345678"
        )

    @patch("elipy2.filer.FilerUtils.delete_with_onefs_api")
    @patch("elipy2.expire.LOGGER")
    def test_delete_build_from_disk_onefs_api(self, mock_logger, mock_delete_onefs):
        """Test _delete_build_from_disk with OneFS API."""
        expire_utils = ExpireUtils()

        expire_utils._delete_build_from_disk("\\\\server\\share\\builds\\test\\12345678", True)

        mock_delete_onefs.assert_called_once_with("\\\\server\\share\\builds\\test\\12345678")
        mock_logger.info.assert_called_with(
            "deleting from disk: file:%s", "\\\\server\\share\\builds\\test\\12345678"
        )

    @patch("elipy2.expire.LOGGER")
    def test_process_build_deletions_dry_run(self, mock_logger):
        """Test _process_build_deletions with dry run."""
        expire_utils = ExpireUtils()
        builds = [
            "\\\\server\\share\\builds\\test\\12345678",
            "\\\\server\\share\\builds\\test\\87654321",
        ]

        with patch.object(expire_utils, "_cleanup_empty_directories") as mock_cleanup:
            failed_deletions = expire_utils._process_build_deletions(
                builds, dry_run=True, use_onefs_api=False, delete_from_bilbo=False
            )

            # Should not fail any deletions
            assert failed_deletions == []

            # Should clean up with dry run
            mock_cleanup.assert_called_once_with(builds, True)

    @patch("elipy2.expire.LOGGER")
    def test_process_build_deletions_invalid_filename(self, mock_logger):
        """Test _process_build_deletions with invalid filename."""
        expire_utils = ExpireUtils()
        builds = ["\\\\server\\share\\builds\\test\\12345?678"]

        with patch.object(expire_utils, "_cleanup_empty_directories") as mock_cleanup:
            failed_deletions = expire_utils._process_build_deletions(
                builds, dry_run=False, use_onefs_api=False, delete_from_bilbo=False
            )

            # Should not fail any deletions (invalid files are skipped)
            assert failed_deletions == []

            # Should not clean up (no valid deletions)
            mock_cleanup.assert_not_called()

    @patch("elipy2.expire.LOGGER")
    def test_process_build_deletions_success(self, mock_logger):
        """Test _process_build_deletions with successful deletion."""
        expire_utils = ExpireUtils()
        builds = ["\\\\server\\share\\builds\\test\\12345678"]

        with patch.object(expire_utils, "_delete_build_from_disk") as mock_delete_disk:
            with patch.object(
                expire_utils, "_delete_all_matching_builds_from_bilbo"
            ) as mock_delete_bilbo:
                with patch.object(expire_utils, "_cleanup_empty_directories") as mock_cleanup:
                    failed_deletions = expire_utils._process_build_deletions(
                        builds, dry_run=False, use_onefs_api=False, delete_from_bilbo=True
                    )

                    # Should not fail any deletions
                    assert failed_deletions == []

                    # Should delete from disk and bilbo
                    mock_delete_disk.assert_called_once()
                    mock_delete_bilbo.assert_called_once()
                    mock_cleanup.assert_called_once()

    @patch("elipy2.expire.LOGGER")
    def test_process_build_deletions_failure(self, mock_logger):
        """Test _process_build_deletions with deletion failure."""
        expire_utils = ExpireUtils()
        builds = ["\\\\server\\share\\builds\\test\\12345678"]

        with patch.object(
            expire_utils, "_delete_build_from_disk", side_effect=ELIPYException("Deletion failed")
        ):
            with patch.object(expire_utils, "_cleanup_empty_directories") as mock_cleanup:
                failed_deletions = expire_utils._process_build_deletions(
                    builds, dry_run=False, use_onefs_api=False, delete_from_bilbo=False
                )

                # Should have one failed deletion
                assert len(failed_deletions) == 1
                assert failed_deletions[0]["build"] == builds[0]
                assert str(failed_deletions[0]["exc"]) == "Deletion failed"

                # Should not clean up (no successful deletions)
                mock_cleanup.assert_not_called()
