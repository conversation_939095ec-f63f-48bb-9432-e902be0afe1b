"""
test_deleter.py

Unit testing for deleter
"""

from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
import os
from dice_elipy_scripts.deleter import (
    cli,
    get_branch_set_under_path,
    cleanup_builds,
    filter_categories,
    check_and_drop_records,
    keep_n_at_azure_path,
    cleanup_azure_retention_paths,
)
from dice_elipy_scripts.tests.testutils import MockResponse

from elipy2.config import ConfigManager
import mock
from mock import patch, MagicMock, ANY, call
import pytest
from unittest import TestCase

from elipy2.exceptions import ConfigValueNotFoundException

config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")


class TestDeleter:
    """
    Only tests the delete empty folders functionality so far.
    """

    maxDiff = None

    @patch("dice_elipy_scripts.deleter.cleanup_shift", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_retention_paths", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_builds", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_avalanche_records", return_value=[])
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_delete_empty_folders(self, mock_thread_pool, _q, _w, _e, _r):
        """
        test_delete_empty_folders
        """
        CliRunner().invoke(cli, ["--empty-folders"])

        assert mock_thread_pool.call_count == 0

    @patch("dice_elipy_scripts.deleter.cleanup_shift", MagicMock(return_value=[]))
    @patch("dice_elipy_scripts.deleter.delete_empty_folders", MagicMock(return_value=[]))
    @patch("dice_elipy_scripts.deleter.cleanup_builds", MagicMock(return_value=[]))
    @patch(
        "dice_elipy_scripts.deleter.cleanup_avalanche_records",
        MagicMock(return_value=[]),
    )
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    @patch("dice_elipy_scripts.deleter.cleanup_retention_paths")
    def test_no_path_retention(self, mock_cleanup_retention_paths, mock_thread_pool):
        CliRunner().invoke(cli, ["--include-path-retention"])
        assert mock_thread_pool.call_count == 0

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_code_paths(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\Code"
        mock_exists.return_value = True
        mock_listdir.return_value = ["branch1", "branch2", "branch3"]

        # Mock os.path.isdir to return True for all items
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 3

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_short_frosty_licensee_paths(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\frosty\\licensee"
        mock_exists.return_value = True
        mock_listdir.return_value = ["branch1", "branch2", "branch3"]

        # Mock os.path.isdir to return True for all items and _is_frosty_branch_directory to return True
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True), patch(
            "dice_elipy_scripts.deleter._is_frosty_branch_directory", return_value=True
        ):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 3

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_roboto_path(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\frosty\\roboto"
        mock_exists.return_value = True
        mock_listdir.return_value = ["branch1", "branch2", "branch3"]

        # Mock os.path.isdir to return True for all items and _is_frosty_branch_directory to return True
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True), patch(
            "dice_elipy_scripts.deleter._is_frosty_branch_directory", return_value=True
        ):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 3

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_exc_path(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\frosty\\exc"
        mock_exists.return_value = True
        mock_listdir.return_value = ["branch1", "branch2", "branch3"]

        # Mock os.path.isdir to return True for all items and _is_frosty_branch_directory to return True
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True), patch(
            "dice_elipy_scripts.deleter._is_frosty_branch_directory", return_value=True
        ):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 3

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_excalibur_path_bug(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        """
        Because this ended in `excalibur`, `get_branch_set_under_path` thinks `autobuilds`
        is a branch. the function should return 3, but instead it returns 4

        I don't think the bug will cause any issue unless we have a `branch` with the same project
        name (e.g. if Excalibur created an `excalibur` branch, or if Kingston created a `kingston` branch)
        """
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\frosty\\excalibur"
        mock_exists.return_value = True
        # Simulate the bug by returning 4 items instead of 3
        mock_listdir.return_value = ["branch1", "branch2", "branch3", "autobuilds"]

        # Mock os.path.isdir to return True for all items and _is_frosty_branch_directory to return True
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True), patch(
            "dice_elipy_scripts.deleter._is_frosty_branch_directory", return_value=True
        ):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 4  # should be 3, but there is a bug

    @patch("dice_elipy_scripts.deleter.os.path.exists")
    @patch("dice_elipy_scripts.deleter.os.listdir")
    def test_get_branch_set_under_path_with_full_frosty_paths(
        self, mock_listdir, mock_exists, fixture_metadata_manager
    ):
        base_path = "\\\\eauk-file.eu.ad.ea.com\\Excalibur\\Autobuilds\\Cobra\\frosty\\licensee"
        mock_exists.return_value = True
        # Only return branch1 since the test expects 1 unique branch
        mock_listdir.return_value = ["branch1"]

        # Mock os.path.isdir to return True for all items and _is_frosty_branch_directory to return True
        with patch("dice_elipy_scripts.deleter.os.path.isdir", return_value=True), patch(
            "dice_elipy_scripts.deleter._is_frosty_branch_directory", return_value=True
        ):
            branch_set = get_branch_set_under_path(base_path)
        assert len(branch_set) == 1

    def test_filter_categories(self):
        categories = {
            "code": [],
            "code_nomaster": [],
            "tnt_local": [],
            "symbols": [],
            "avalanchestate": [],
            "frosty/kingston": [],
            "ant_cache": [],
            "webexport": [],
            "default": [],
        }
        filtered = filter_categories(categories)

        assert categories == filtered

    def test_filter_categories_include(self):
        categories = {
            "code": [],
            "code_nomaster": [],
            "tnt_local": [],
            "symbols": [],
            "avalanchestate": [],
            "frosty/kingston": [],
            "ant_cache": [],
            "webexport": [],
            "default": [],
        }
        filtered = filter_categories(categories, ["code"])

        assert {"code": []} == filtered

    def test_filter_categories_exclude(self):
        categories = {
            "code": [],
            "code_nomaster": [],
            "tnt_local": [],
            "symbols": [],
            "avalanchestate": [],
            "frosty/kingston": [],
            "ant_cache": [],
            "webexport": [],
            "default": [],
        }
        filtered = filter_categories(categories, exclude=["code"])
        del categories["code"]
        assert categories == filtered

    def test_filter_categories_include_raises(self):
        categories = {
            "code": [],
        }
        with pytest.raises(ValueError) as exce:
            filter_categories(categories, include=["invalid_cat"])

        assert (
            exce.value.args[0]
            == "Invalid values passed for include (['invalid_cat']) or exclude ([])"
        )

    def test_filter_categories_exclude_raises(self):
        categories = {
            "code": [],
        }
        with pytest.raises(ValueError) as exce:
            filter_categories(categories, exclude=["invalid_cat"])

        assert (
            exce.value.args[0]
            == "Invalid values passed for include ([]) or exclude (['invalid_cat'])"
        )

    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}

            return branch_set

        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api)

        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
        assert sorted(map_args) == sorted(expected)
        assert len(map_args) == 4

    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_use_onefs_api(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}
            return branch_set

        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = False
        use_onefs_api = True
        cleanup_builds(dry_run, use_onefs_api)

        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
        assert sorted(map_args) == sorted(expected)
        assert len(map_args) == 4

    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_includes(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code") or path.endswith("casablanca"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}

            return branch_set

        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api, include=["code"])

        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1",
                6,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4",
                9,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2",
                7,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3",
                8,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
        assert sorted(map_args) == sorted(expected)
        assert len(map_args) == 4

    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.ThreadPool")
    def test_cleanup_builds_with_code_builds_excludes(
        self, mock_thread_pool, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code") or path.endswith("casablanca"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}

            return branch_set

        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = True
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api, exclude=["code"])

        assert mock_thread_pool.call_count == 1
        map_args = mock_thread_pool.return_value.map.mock_calls[0][1][1]
        expected = [
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1",
                21,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4",
                24,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2",
                22,
                dry_run,
                use_onefs_api,
                56,
            ),
            (
                "\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3",
                23,
                dry_run,
                use_onefs_api,
                56,
            ),
        ]
        assert sorted(map_args) == sorted(expected)
        assert len(map_args) == 4

    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.get_branch_set_under_path")
    @patch("dice_elipy_scripts.deleter.core")
    def test_cleanup_builds_with_code_builds_no_bilbo(
        self, mock_core, mock_get_branch_set_under_path
    ):
        def fake_get_branch_set(path):
            branch_set = set()
            if path.endswith("code") or path.endswith("casablanca"):
                branch_set = {"branch1", "branch2", "branch3", "branch4"}

            return branch_set

        mock_get_branch_set_under_path.side_effect = fake_get_branch_set
        dry_run = False
        use_onefs_api = False
        cleanup_builds(dry_run, use_onefs_api, include=["code"], creation_time_deletion=True)

        assert mock_core.call_count == 0

    @patch("elipy2.avalanche.get_avalanche_free_space", MagicMock(return_value=10))
    @patch("elipy2.avalanche.six.moves.urllib.request.urlopen")
    def test_check_and_drop_records_not_records_returned(self, mock_urlopen):
        mock_urlopen.side_effect = [
            MockResponse([]),
            MockResponse(
                {
                    "Primary": {
                        "pools": [{"freeDiskSpace": 2987698995, "totalDiskSpace": 29876989952}]
                    }
                }
            ),
        ]
        check_and_drop_records("testing_avalanche_instance", False, 30)

    @patch("dice_elipy_scripts.deleter.cleanup_shift", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_retention_paths", return_value=[])
    @patch("dice_elipy_scripts.deleter.cleanup_avalanche_records", return_value=[])
    @patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path))
    @patch("dice_elipy_scripts.deleter.cleanup_builds")
    def test_exclude_retention_categories(self, mock_cleanup_builds, _q, _w, _e):
        result = CliRunner().invoke(cli, ["--exclude-retention-categories"])
        mock_cleanup_builds.assert_not_called()

    @patch("dice_elipy_scripts.deleter.AZCopyClient")
    @patch("dice_elipy_scripts.deleter.az_utils.yield_azfileshare_path_contents_metadata")
    def test_keep_n_at_azure_path_generates_urls_correctly_keep_1(
        self,
        mock_yield_dir_metadata,
        mock_AZCopyClient,
    ):
        # Test case: out of 9 directories, 8 should be deleted (keep 1)
        account_name = "account_name"
        share_name = "share_name"
        fileshare_path = "path/to/fileshare/dir"
        maxamount = 1
        azure_domain = "fake.azure.domain.net"
        expected_deleted_dirs = ["121", "122", "123", "124", "125", "126", "127", "128"]

        def mock_files_and_dirs_generator():
            # unordered list of directories
            yield {"name": "121", "is_directory": True}
            yield {"name": "122", "is_directory": True}
            yield {"name": "123", "is_directory": True}
            yield {"name": "124", "is_directory": True}
            yield {"name": "125", "is_directory": True}
            yield {"name": "126", "is_directory": True}
            yield {"name": "127", "is_directory": True}
            yield {"name": "128", "is_directory": True}
            yield {"name": "129", "is_directory": True}

        mock_yield_dir_metadata.return_value = mock_files_and_dirs_generator()

        mock_azcopy_client = mock_AZCopyClient.return_value = MagicMock()
        mock_azcopy_client.account_name = account_name
        mock_azcopy_client.remove_dir = MagicMock()
        num_dirs = len(list(mock_files_and_dirs_generator()))

        _ = keep_n_at_azure_path(
            fileshare_path=fileshare_path,
            maxamount=maxamount,
            dry_run=False,
            secret_context="my_secret_context",
            fileshare_name=share_name,
            azure_domain="fake.azure.domain.net",
        )

        for _ in mock_files_and_dirs_generator():
            assert mock_azcopy_client.remove_dir.call_count == num_dirs - maxamount
        for dir_name in expected_deleted_dirs:
            expected = (
                f"https://{account_name}.{azure_domain}/{share_name}/{fileshare_path}/{dir_name}"
            )
            mock_azcopy_client.remove_dir.assert_any_call(expected)

    @patch("dice_elipy_scripts.deleter.AZCopyClient")
    @patch("dice_elipy_scripts.deleter.az_utils.yield_azfileshare_path_contents_metadata")
    def test_keep_n_at_azure_path_generates_urls_correctly_keep_8(
        self,
        mock_yield_dir_metadata,
        mock_AZCopyClient,
    ):
        # Test case: out of 9 directories, only 1 should be deleted (keep 8)
        account_name = "account_name"
        share_name = "share_name"
        fileshare_path = "path/to/fileshare/dir"
        maxamount = 8
        azure_domain = "fake.azure.domain.net"
        expected_deleted_dirs = ["121"]

        def mock_files_and_dirs_generator():
            # unordered list of directories
            yield {"name": "121", "is_directory": True}
            yield {"name": "122", "is_directory": True}
            yield {"name": "123", "is_directory": True}
            yield {"name": "124", "is_directory": True}
            yield {"name": "125", "is_directory": True}
            yield {"name": "126", "is_directory": True}
            yield {"name": "127", "is_directory": True}
            yield {"name": "128", "is_directory": True}
            yield {"name": "129", "is_directory": True}

        mock_yield_dir_metadata.return_value = mock_files_and_dirs_generator()

        mock_azcopy_client = mock_AZCopyClient.return_value = MagicMock()
        mock_azcopy_client.account_name = account_name
        mock_azcopy_client.remove_dir = MagicMock()
        num_dirs = len(list(mock_files_and_dirs_generator()))

        _ = keep_n_at_azure_path(
            fileshare_path=fileshare_path,
            maxamount=maxamount,
            dry_run=False,
            secret_context="my_secret_context",
            fileshare_name=share_name,
            azure_domain="fake.azure.domain.net",
        )

        for _ in mock_files_and_dirs_generator():
            assert mock_azcopy_client.remove_dir.call_count == num_dirs - maxamount
        for dir_name in expected_deleted_dirs:
            expected = (
                f"https://{account_name}.{azure_domain}/{share_name}/{fileshare_path}/{dir_name}"
            )
            mock_azcopy_client.remove_dir.assert_any_call(expected)

    @patch("dice_elipy_scripts.deleter.AZCopyClient")
    @patch("dice_elipy_scripts.deleter.az_utils.yield_azfileshare_path_contents_metadata")
    def test_keep_n_at_azure_path_generates_urls_correctly_keep_6(
        self,
        mock_yield_dir_metadata,
        mock_AZCopyClient,
    ):
        # Test case: out of 9 directories, 3 should be deleted (keep 6)
        account_name = "account_name"
        share_name = "share_name"
        fileshare_path = "path/to/fileshare/dir"
        maxamount = 6
        azure_domain = "fake.azure.domain.net"
        expected_deleted_dirs = ["121", "122", "123"]

        def mock_files_and_dirs_generator():
            # unordered list of directories
            yield {"name": "121", "is_directory": True}
            yield {"name": "122", "is_directory": True}
            yield {"name": "123", "is_directory": True}
            yield {"name": "124", "is_directory": True}
            yield {"name": "125", "is_directory": True}
            yield {"name": "126", "is_directory": True}
            yield {"name": "127", "is_directory": True}
            yield {"name": "128", "is_directory": True}
            yield {"name": "129", "is_directory": True}

        mock_yield_dir_metadata.return_value = mock_files_and_dirs_generator()

        mock_azcopy_client = mock_AZCopyClient.return_value = MagicMock()
        mock_azcopy_client.account_name = account_name
        mock_azcopy_client.remove_dir = MagicMock()
        num_dirs = len(list(mock_files_and_dirs_generator()))

        _ = keep_n_at_azure_path(
            fileshare_path=fileshare_path,
            maxamount=maxamount,
            dry_run=False,
            secret_context="my_secret_context",
            fileshare_name=share_name,
            azure_domain="fake.azure.domain.net",
        )

        for _ in mock_files_and_dirs_generator():
            assert mock_azcopy_client.remove_dir.call_count == num_dirs - maxamount
        for dir_name in expected_deleted_dirs:
            expected = (
                f"https://{account_name}.{azure_domain}/{share_name}/{fileshare_path}/{dir_name}"
            )
            mock_azcopy_client.remove_dir.assert_any_call(expected)

    @patch("dice_elipy_scripts.deleter.AZCopyClient")
    @patch("dice_elipy_scripts.deleter.az_utils.yield_azfileshare_path_contents_metadata")
    def test_keep_n_at_azure_path_dry_run_behavior_true(
        self,
        mock_yield_dir_metadata,
        mock_AZCopyClient,
    ):
        # Test case: dry_run=True, expected=0
        is_dry_run = True
        expected = 0

        def mock_files_and_dirs_generator():
            # unordered list of directories
            yield {"name": "121", "is_directory": True}
            yield {"name": "122", "is_directory": True}

        mock_yield_dir_metadata.return_value = mock_files_and_dirs_generator()

        mock_azcopy_client = mock_AZCopyClient.return_value = MagicMock()
        mock_azcopy_client.account_name = "account_name"
        mock_azcopy_client.share_name = "share_name"
        mock_azcopy_client.remove_dir = MagicMock()

        _ = keep_n_at_azure_path(
            "path/to/fileshare/dir",
            1,
            is_dry_run,
            "my_secret_context",
            fileshare_name="share_name",
            azure_domain="fake.azure.domain.net",
        )

        call_count = mock_azcopy_client.remove_dir.call_count
        assert call_count == expected

    @patch("dice_elipy_scripts.deleter.AZCopyClient")
    @patch("dice_elipy_scripts.deleter.az_utils.yield_azfileshare_path_contents_metadata")
    def test_keep_n_at_azure_path_dry_run_behavior_false(
        self,
        mock_yield_dir_metadata,
        mock_AZCopyClient,
    ):
        # Test case: dry_run=False, expected=1
        is_dry_run = False
        expected = 1

        def mock_files_and_dirs_generator():
            # unordered list of directories
            yield {"name": "121", "is_directory": True}
            yield {"name": "122", "is_directory": True}

        mock_yield_dir_metadata.return_value = mock_files_and_dirs_generator()

        mock_azcopy_client = mock_AZCopyClient.return_value = MagicMock()
        mock_azcopy_client.account_name = "account_name"
        mock_azcopy_client.share_name = "share_name"
        mock_azcopy_client.remove_dir = MagicMock()

        _ = keep_n_at_azure_path(
            "path/to/fileshare/dir",
            1,
            is_dry_run,
            "my_secret_context",
            fileshare_name="share_name",
            azure_domain="fake.azure.domain.net",
        )

        call_count = mock_azcopy_client.remove_dir.call_count
        assert call_count == expected

    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_true(
        self, mock_settings
    ):
        # Test case: include_azure_path_retention=True, expected=True
        include_azure_path_retention = True
        expected = True

        mock_settings_get = mock_settings.get = MagicMock()
        cleanup_azure_retention_paths(include_azure_path_retention, True)
        assert mock_settings_get.called == expected

    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_false(
        self, mock_settings
    ):
        # Test case: include_azure_path_retention=False, expected=False
        include_azure_path_retention = False
        expected = False

        mock_settings_get = mock_settings.get = MagicMock()
        cleanup_azure_retention_paths(include_azure_path_retention, True)
        assert mock_settings_get.called == expected

    @patch("dice_elipy_scripts.deleter.keep_n_at_azure_path")
    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_cleanup_azure_retention_paths_handles_no_config_empty_string(
        self, mock_settings, mock_keep_n_at_azure_path
    ):
        # Test case: setting_get_return_value="", expected=False
        setting_get_return_value = ""
        expected = False

        mock_settings.get.return_value = setting_get_return_value

        include_azure_path_retention = True
        dry_run = False

        cleanup_azure_retention_paths(include_azure_path_retention, dry_run=dry_run)
        assert mock_keep_n_at_azure_path.called == expected

    @patch("dice_elipy_scripts.deleter.keep_n_at_azure_path")
    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_cleanup_azure_retention_paths_handles_no_config_none(
        self, mock_settings, mock_keep_n_at_azure_path
    ):
        # Test case: setting_get_return_value=None, expected=False
        setting_get_return_value = None
        expected = False

        mock_settings.get.return_value = setting_get_return_value

        include_azure_path_retention = True
        dry_run = False

        cleanup_azure_retention_paths(include_azure_path_retention, dry_run=dry_run)
        assert mock_keep_n_at_azure_path.called == expected

    @patch("dice_elipy_scripts.deleter.keep_n_at_azure_path", MagicMock())
    @patch("dice_elipy_scripts.deleter.SETTINGS")
    def test_cleanup_azure_retention_paths_passes_if_no_config_found(self, mock_settings):
        mock_settings.get = MagicMock(side_effect=ConfigValueNotFoundException)
        try:
            result = cleanup_azure_retention_paths(True, False)
        except ConfigValueNotFoundException:
            pytest.fail("ConfigValueNotFoundException should pass but it raised the exception.")

        # assuming the exception is raised, the function should still return the exceptions list
        assert result == []

    @patch("dice_elipy_scripts.deleter.keep_n_at_azure_path")
    def test_settings_file_iterates_over_multiple_shares_in_storage_account(
        self, mock_keep_n_at_azure_path
    ):
        config_path = os.path.join(os.path.dirname(__file__), "data", "elipy_test.yml")
        with patch("dice_elipy_scripts.deleter.SETTINGS", ConfigManager(path=config_path)):
            cleanup_azure_retention_paths(include_azure_path_retention=True, dry_run=False)
            assert mock_keep_n_at_azure_path.call_count == 2
