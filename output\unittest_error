=========================== short test summary info ============================
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_code_paths - assert 0 == 3
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_short_frosty_licensee_paths - assert 0 == 3
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_roboto_path - assert 0 == 3
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_exc_path - assert 0 == 3
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_excalibur_path_bug - assert 0 == 4
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_full_frosty_paths - assert 0 == 1
 +  where 0 = len(set())
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      True,
      False,
      56),
    ]
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, False, True, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, False, True, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      False,
      True,
      56),
    ]
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      True,
      False,
      56),
    ]/usr/local/lib/python3.7/site-packages/setuptools/__init__.py:84: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  dist.fetch_build_eggs(dist.setup_requires)
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:194: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  ir_d = dist.fetch_build_eggs(dist.install_requires)
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:195: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  tr_d = dist.fetch_build_eggs(dist.tests_require or [])
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:198: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  for k, v in dist.extras_require.items()
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4', 24, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4', 24, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1',
  ?                                                     ^                  ^
      21,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2',
  ?                                                     ^                  ^
      22,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3',
  ?                                                     ^                  ^
      23,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4',
  ?                                                     ^                  ^
      24,
      True,
      False,
      56),
    ]
========= 10 failed, 1070 passed, 39 deselected, 4 warnings in 31.62s ==========

=========================== short test summary info ============================
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      True,
      False,
      56),
    ]
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, False, True, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, False, True, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, False, True, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, False, True, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, False, True, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      False,
      True,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      False,
      True,
      56),
    ]
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4', 9, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2', 7, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3', 8, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4', 9, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1', 6, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1', 6, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch1',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch1',
  ?                                                     ^    ^
      6,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch2',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch2',
  ?                                                     ^    ^
      7,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch3',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch3',
  ?                                                     ^    ^
      8,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\code\\branch4',
  ?                                                     ^^    ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/code/branch4',
  ?                                                     ^    ^
      9,
      True,
      False,
      56),
    ]/usr/local/lib/python3.7/site-packages/setuptools/__init__.py:84: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  dist.fetch_build_eggs(dist.setup_requires)
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:194: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  ir_d = dist.fetch_build_eggs(dist.install_requires)
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:195: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  tr_d = dist.fetch_build_eggs(dist.tests_require or [])
/usr/local/lib/python3.7/site-packages/setuptools/command/test.py:198: _DeprecatedInstaller: setuptools.installer and fetch_build_eggs are deprecated.
!!
        ********************************************************************************
        Requirements should be satisfied by a PEP 517 installer.
        If you are using pip, you can try `pip install --use-pep517`.
        ********************************************************************************
!!
  for k, v in dist.extras_require.items()
FAILED dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes - AssertionError: assert [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4', 24, True, False, 56)] == [('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2', 22, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3', 23, True, False, 56), ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4', 24, True, False, 56)]
  At index 0 diff: ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1', 21, True, False, 56) != ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1', 21, True, False, 56)
  Full diff:
    [
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch1',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch1',
  ?                                                     ^                  ^
      21,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch2',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch2',
  ?                                                     ^                  ^
      22,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch3',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch3',
  ?                                                     ^                  ^
      23,
      True,
      False,
      56),
  -  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca\\frosty\\casablanca\\branch4',
  ?                                                     ^^                  ^^
  +  ('\\\\fake-filer.dice.ad.ea.com\\builds\\Casablanca/frosty\\casablanca/branch4',
  ?                                                     ^                  ^
      24,
      True,
      False,
      56),
    ]
========== 4 failed, 1070 passed, 39 deselected, 4 warnings in 32.50s ==========