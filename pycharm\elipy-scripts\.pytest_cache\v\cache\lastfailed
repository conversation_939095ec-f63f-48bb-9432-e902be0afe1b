{"dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_cli_basic_parameters_validation": true, "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_create_head_bundles_path_handling": true, "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_custom_network_share_path": true, "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_delta_bundle_parameters_validation": true, "dice_elipy_scripts/tests/test_combined_bundle_creator.py::TestCombinedBundleCreator::test_process_bundles_for_combination": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes": true}